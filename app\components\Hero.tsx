"use client";
import { motion } from "framer-motion";

export default function Hero() {
  return (
    <section
      className="relative h-[90vh] w-full bg-cover bg-center"
      style={{ backgroundImage: "url(/images/kuriftu-hero.jpg)" }}
    >
      <div className="absolute inset-0 bg-black/50" />
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 1 }}
        className="relative z-10 h-full flex flex-col justify-center items-center text-center text-white px-4"
      >
        <h1 className="text-4xl md:text-6xl font-bold mb-4">
          Welcome to Kuriftu Resorts & Spa
        </h1>
        <p className="text-lg md:text-2xl mb-6">
          Luxury, Culture, and Wellness in One Destination
        </p>
        <button className="bg-white text-black px-6 py-3 rounded-xl text-lg font-semibold hover:bg-gray-200 transition">
          Explore Resorts
        </button>
      </motion.div>
    </section>
  );
}
