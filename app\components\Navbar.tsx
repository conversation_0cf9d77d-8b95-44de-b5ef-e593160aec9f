"use client";

import Link from "next/link";
import { useSession, signIn, signOut } from "next-auth/react";
import { useRole } from "./auth/RoleGuard";

export default function Navbar() {
  const { data: session } = useSession();
  const { isAdmin, is<PERSON><PERSON><PERSON>, isReceptionist, isStaff } = useRole();

  return (
    <nav className="bg-white shadow-md py-3 px-6 flex justify-between items-center">
      <Link href="/" className="text-xl font-bold text-primary">
        Kuriftu Resorts
      </Link>

      <div className="space-x-4 flex items-center">
        <Link href="/resorts" className="hover:underline">
          Resorts
        </Link>
        <Link href="/spa" className="hover:underline">
          Spa
        </Link>

        {session && (
          <Link href="/bookings" className="hover:underline">
            My Bookings
          </Link>
        )}

        {/* Role-based navigation */}
        {isAdmin() && (
          <Link
            href="/admin"
            className="hover:underline text-purple-600 font-medium"
          >
            Admin Panel
          </Link>
        )}

        {isManager() && (
          <Link
            href="/manager"
            className="hover:underline text-blue-600 font-medium"
          >
            Manager Dashboard
          </Link>
        )}

        {isReceptionist() && (
          <Link
            href="/reception"
            className="hover:underline text-green-600 font-medium"
          >
            Reception
          </Link>
        )}

        {session ? (
          <div className="flex items-center space-x-3">
            <span className="text-gray-600">
              Hi, {session.user?.name?.split(" ")[0]}
              {isStaff() && (
                <span className="ml-1 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                  {session.user.role}
                </span>
              )}
            </span>
            <button
              type="button"
              onClick={() => signOut()}
              className="text-red-500 hover:underline"
            >
              Sign Out
            </button>
          </div>
        ) : (
          <button
            type="button"
            onClick={() => signIn("google")}
            className="text-blue-600 hover:underline"
          >
            Sign In
          </button>
        )}
      </div>
    </nav>
  );
}
