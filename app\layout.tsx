import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { ReactNode } from "react";
import Navbar from "./components/Navbar";
import { SessionProvider } from "next-auth/react";
import Footer from "./components/Footer";
import { ToastContainer } from "react-toast";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en">
      <body>
        <SessionProvider>
          <Navbar />
          <main className="pt-4">{children}</main>
          <Footer />
          <ToastContainer position="top-right" delay={4000} />
        </SessionProvider>
      </body>
    </html>
  );
}
