import { prisma } from "@/lib/prisma";
import Image from "next/image";
import { notFound } from "next/navigation";

export default async function ResortDetail({
  params,
}: {
  params: { slug: string };
}) {
  const resort = await prisma.resort.findUnique({
    where: { slug: params.slug },
  });

  if (!resort) return notFound();

  return (
    <div className="max-w-5xl mx-auto px-4 py-10">
      <Image
        src={resort.image}
        alt={resort.name}
        className="w-full h-[400px] object-cover rounded-xl mb-6"
      />
      <h1 className="text-4xl font-bold mb-4">{resort.name}</h1>
      <p className="text-lg">{resort.description}</p>
    </div>
  );
}
