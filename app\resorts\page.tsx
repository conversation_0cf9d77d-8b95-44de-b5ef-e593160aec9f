import { prisma } from "@/lib/prisma";
import Image from "next/image";
import Link from "next/link";
import DynamicSpaServiceCards from "@/app/components/DynamicSpaServiceCards";

export default async function ResortsListPage() {
  const resorts = await prisma.resort.findMany();

  return (
    <div className="max-w-6xl mx-auto px-4 py-10">
      {/* Resorts Section */}
      <section className="mb-16">
        <h1 className="text-3xl font-bold text-center mb-10">Our Resorts</h1>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
          {resorts.map((resort) => (
            <Link
              key={resort.id}
              href={`/resorts/${resort.slug}`}
              className="block bg-white rounded-xl shadow hover:shadow-md transition"
            >
              <Image
                src={resort.image}
                alt={resort.name}
                className="w-full h-48 object-cover rounded-t-xl"
                width={300}
                height={200}
              />
              <div className="p-4 text-center font-semibold">{resort.name}</div>
            </Link>
          ))}
        </div>
      </section>

      {/* Spa Services Section */}
      <DynamicSpaServiceCards
        enableRealTimeUpdates={true}
        updateInterval={30000}
        showTitle={true}
      />
    </div>
  );
}
