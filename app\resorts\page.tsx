import { prisma } from "@/lib/prisma";
import Image from "next/image";
import Link from "next/link";
import SpaServiceCard from "@/app/components/SpaServiceCard";
import { SpaService } from "@/app/types/spa";

// Spa services data with placeholder images that will trigger fallback
const spaServices: SpaService[] = [
  {
    id: "1",
    name: "Professional Barber Service",
    description:
      "Expert haircuts, beard trimming, and traditional shaving services with premium grooming products.",
    price: 150,
    duration: 45,
    image:
      "https://images.unsplash.com/photo-1503951914875-452162b0f3f1?w=400&h=300&fit=crop",
    category: "Grooming",
    features: ["Haircut", "Beard Trim", "Hot Towel Shave"],
  },
  {
    id: "2",
    name: "Steam Sauna Experience",
    description:
      "Relax and detoxify in our traditional steam sauna with eucalyptus aromatherapy.",
    price: 200,
    duration: 60,
    image:
      "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop",
    category: "Wellness",
    features: ["Steam Therapy", "Aromatherapy", "Detoxification"],
  },
  {
    id: "3",
    name: "Professional Waxing",
    description:
      "Gentle and effective hair removal services using premium wax for smooth, long-lasting results.",
    price: 120,
    duration: 30,
    image:
      "https://images.unsplash.com/photo-1560750588-73207b1ef5b8?w=400&h=300&fit=crop",
    category: "Beauty",
    features: ["Full Body", "Facial", "Precision Waxing"],
  },
  {
    id: "4",
    name: "Rejuvenating Facial",
    description:
      "Deep cleansing and moisturizing facial treatment to refresh and revitalize your skin.",
    price: 250,
    duration: 75,
    image:
      "https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400&h=300&fit=crop",
    category: "Skincare",
    features: ["Deep Cleansing", "Moisturizing", "Anti-Aging"],
  },
  {
    id: "5",
    name: "Therapeutic Massage",
    description:
      "Full-body relaxation massage to relieve stress and tension using traditional techniques.",
    price: 300,
    duration: 90,
    image:
      "https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400&h=300&fit=crop",
    category: "Therapy",
    features: ["Swedish Massage", "Deep Tissue", "Aromatherapy"],
  },
  {
    id: "6",
    name: "Luxury Pedicure",
    description:
      "Complete foot care treatment including nail shaping, cuticle care, and relaxing foot massage.",
    price: 180,
    duration: 60,
    image:
      "https://images.unsplash.com/photo-1519415943484-9fa1873496d4?w=400&h=300&fit=crop",
    category: "Beauty",
    features: ["Nail Care", "Foot Massage", "Moisturizing"],
  },
  {
    id: "7",
    name: "Professional Manicure",
    description:
      "Complete hand and nail care with nail shaping, cuticle treatment, and hand massage.",
    price: 120,
    duration: 45,
    image:
      "https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400&h=300&fit=crop",
    category: "Beauty",
    features: ["Nail Shaping", "Cuticle Care", "Hand Massage"],
  },
  {
    id: "8",
    name: "Body Scrub & Wrap",
    description:
      "Exfoliating body scrub followed by nourishing body wrap for silky smooth skin.",
    price: 280,
    duration: 80,
    image:
      "https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=400&h=300&fit=crop",
    category: "Skincare",
    features: ["Exfoliation", "Body Wrap", "Moisturizing"],
  },
];

export default async function ResortsListPage() {
  const resorts = await prisma.resort.findMany();

  return (
    <div className="max-w-6xl mx-auto px-4 py-10">
      {/* Resorts Section */}
      <section className="mb-16">
        <h1 className="text-3xl font-bold text-center mb-10">Our Resorts</h1>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
          {resorts.map((resort) => (
            <Link
              key={resort.id}
              href={`/resorts/${resort.slug}`}
              className="block bg-white rounded-xl shadow hover:shadow-md transition"
            >
              <Image
                src={resort.image}
                alt={resort.name}
                className="w-full h-48 object-cover rounded-t-xl"
                width={300}
                height={200}
              />
              <div className="p-4 text-center font-semibold">{resort.name}</div>
            </Link>
          ))}
        </div>
      </section>

      {/* Spa Services Section */}
      <section>
        <div className="text-center mb-10">
          <h2 className="text-3xl font-bold mb-4">Premium Spa Services</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Indulge in our world-class spa treatments designed to rejuvenate
            your body and mind. Experience luxury and relaxation with our
            professional spa services.
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {spaServices.map((service) => (
            <SpaServiceCard key={service.id} service={service} />
          ))}
        </div>
      </section>
    </div>
  );
}
