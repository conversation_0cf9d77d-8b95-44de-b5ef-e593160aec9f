import { prisma } from "@/lib/prisma";
import { notFound } from "next/navigation";
import Image from "next/image";
import Link from "next/link";

interface SpaDetailsPageProps {
  params: {
    id: string;
  };
}

export default async function SpaDetailsPage({ params }: SpaDetailsPageProps) {
  let treatment;

  try {
    treatment = await prisma.spaTreatment.findUnique({
      where: { id: params.id },
    });

    if (!treatment) {
      notFound();
    }
  } catch (error) {
    console.error("Error fetching spa treatment:", error);
    notFound();
  }

  return (
    <div className="max-w-6xl mx-auto px-4 py-10">
      {/* Breadcrumb */}
      <nav className="mb-8">
        <ol className="flex items-center space-x-2 text-sm text-gray-600">
          <li>
            <Link href="/" className="hover:text-blue-600">
              Home
            </Link>
          </li>
          <li>/</li>
          <li>
            <Link href="/spa" className="hover:text-blue-600">
              Spa
            </Link>
          </li>
          <li>/</li>
          <li className="text-gray-900 font-medium">{treatment.name}</li>
        </ol>
      </nav>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Image Section */}
        <div className="space-y-4">
          <div className="relative aspect-square rounded-xl overflow-hidden">
            <Image
              src={treatment.image}
              alt={treatment.name}
              fill
              className="object-cover"
              priority
            />
          </div>
        </div>

        {/* Details Section */}
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {treatment.name}
            </h1>
            {treatment.category && (
              <span className="inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                {treatment.category}
              </span>
            )}
          </div>

          <div className="text-lg text-gray-600">
            {treatment.description}
          </div>

          {/* Treatment Details */}
          <div className="bg-gray-50 rounded-lg p-6 space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Treatment Details</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="text-sm text-gray-600">Price</span>
                <p className="text-2xl font-bold text-blue-600">{treatment.price} ETB</p>
              </div>
              
              {treatment.duration && (
                <div>
                  <span className="text-sm text-gray-600">Duration</span>
                  <p className="text-lg font-semibold">{treatment.duration} minutes</p>
                </div>
              )}
            </div>

            {treatment.features && treatment.features.length > 0 && (
              <div>
                <span className="text-sm text-gray-600 block mb-2">Includes</span>
                <div className="flex flex-wrap gap-2">
                  {treatment.features.map((feature, index) => (
                    <span
                      key={index}
                      className="bg-white text-gray-700 px-3 py-1 rounded-full text-sm border"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Booking Section */}
          {treatment.isActive && (
            <div className="bg-blue-50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Ready to Book?
              </h3>
              <p className="text-gray-600 mb-4">
                Experience this amazing treatment and rejuvenate your body and mind.
              </p>
              <Link
                href={`/book?spaId=${treatment.id}`}
                className="inline-block bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors"
              >
                Book Now
              </Link>
            </div>
          )}

          {!treatment.isActive && (
            <div className="bg-gray-100 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Currently Unavailable
              </h3>
              <p className="text-gray-600">
                This treatment is temporarily unavailable. Please check back later or contact us for more information.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Additional Information */}
      <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="bg-white rounded-lg p-6 shadow-sm border">
          <h3 className="text-lg font-semibold mb-4">Preparation</h3>
          <ul className="space-y-2 text-sm text-gray-600">
            <li>• Arrive 15 minutes before your appointment</li>
            <li>• Wear comfortable, loose-fitting clothing</li>
            <li>• Avoid heavy meals 2 hours before treatment</li>
            <li>• Stay hydrated throughout the day</li>
          </ul>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border">
          <h3 className="text-lg font-semibold mb-4">What to Expect</h3>
          <ul className="space-y-2 text-sm text-gray-600">
            <li>• Professional consultation before treatment</li>
            <li>• Relaxing and comfortable environment</li>
            <li>• Experienced and certified therapists</li>
            <li>• Post-treatment care instructions</li>
          </ul>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border">
          <h3 className="text-lg font-semibold mb-4">Policies</h3>
          <ul className="space-y-2 text-sm text-gray-600">
            <li>• 24-hour cancellation policy</li>
            <li>• Please inform us of any allergies</li>
            <li>• Gratuity is appreciated but not required</li>
            <li>• Photography is not permitted</li>
          </ul>
        </div>
      </div>

      {/* Back to Spa Services */}
      <div className="mt-12 text-center">
        <Link
          href="/spa"
          className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back to All Spa Services
        </Link>
      </div>
    </div>
  );
}
