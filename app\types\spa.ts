// TypeScript interfaces for spa services and related types

export interface SpaService {
  id: string;
  name: string;
  description: string;
  price: number;
  duration?: number; // Duration in minutes
  image: string;
  category: SpaServiceCategory;
  features?: string[];
  isActive?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export type SpaServiceCategory = 
  | "Grooming" 
  | "Wellness" 
  | "Beauty" 
  | "Skincare" 
  | "Therapy" 
  | "Massage"
  | "Facial"
  | "Body Treatment";

export interface SpaBooking {
  id: string;
  serviceId: string;
  userId: string;
  userEmail: string;
  appointmentDate: Date;
  appointmentTime: string;
  duration: number;
  totalAmount: number;
  status: SpaBookingStatus;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  service?: SpaService;
}

export type SpaBookingStatus = 
  | "PENDING" 
  | "CONFIRMED" 
  | "IN_PROGRESS" 
  | "COMPLETED" 
  | "CANCELLED" 
  | "NO_SHOW";

export interface SpaServiceCardProps {
  service: SpaService;
  isLoading?: boolean;
  showBookButton?: boolean;
  showDetailsButton?: boolean;
  onBook?: (serviceId: string) => void;
  onViewDetails?: (serviceId: string) => void;
  className?: string;
}

export interface SpaServiceFilters {
  category?: SpaServiceCategory;
  priceRange?: {
    min: number;
    max: number;
  };
  duration?: {
    min: number;
    max: number;
  };
  searchTerm?: string;
}

export interface SpaServiceListProps {
  services: SpaService[];
  filters?: SpaServiceFilters;
  isLoading?: boolean;
  onServiceSelect?: (service: SpaService) => void;
  showFilters?: boolean;
  gridCols?: 1 | 2 | 3 | 4;
  className?: string;
}

// Utility type for creating new spa services
export type CreateSpaServiceInput = Omit<SpaService, 'id' | 'createdAt' | 'updatedAt'>;

// Utility type for updating spa services
export type UpdateSpaServiceInput = Partial<CreateSpaServiceInput> & { id: string };
