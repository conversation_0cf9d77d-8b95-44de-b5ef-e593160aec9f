import nodemailer from "nodemailer";
import { logAuditEvent } from "./security";

// Email configuration with security settings
export const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: Number(process.env.EMAIL_PORT),
  secure: process.env.EMAIL_SECURE === "true",
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
  tls: {
    rejectUnauthorized: true,
  },
});

// Enhanced booking confirmation email with proper template
export const sendBookingConfirmation = async (
  userEmail: string,
  bookingData: any
) => {
  const emailTemplate = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Booking Confirmation - Kuriftu Resorts</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .booking-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea; }
        .detail-row { display: flex; justify-content: space-between; margin: 10px 0; padding: 8px 0; border-bottom: 1px solid #eee; }
        .detail-label { font-weight: bold; color: #555; }
        .detail-value { color: #333; }
        .status { padding: 5px 15px; border-radius: 20px; font-size: 12px; font-weight: bold; text-transform: uppercase; }
        .status.pending { background: #fff3cd; color: #856404; }
        .status.confirmed { background: #d4edda; color: #155724; }
        .footer { text-align: center; margin-top: 30px; padding: 20px; color: #666; font-size: 14px; }
        .contact-info { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .button { display: inline-block; padding: 12px 25px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>🏨 Kuriftu Resorts</h1>
        <h2>Booking Confirmation</h2>
      </div>

      <div class="content">
        <p>Dear ${bookingData.customerName || "Valued Guest"},</p>
        <p>Thank you for choosing Kuriftu Resorts! We're delighted to confirm your booking.</p>

        <div class="booking-details">
          <h3>📋 Booking Details</h3>
          <div class="detail-row">
            <span class="detail-label">Booking ID:</span>
            <span class="detail-value">${bookingData.bookingId}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Type:</span>
            <span class="detail-value">${bookingData.type}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">${
              bookingData.type === "Resort" ? "Resort" : "Treatment"
            }:</span>
            <span class="detail-value">${bookingData.name}</span>
          </div>
          ${
            bookingData.location
              ? `
          <div class="detail-row">
            <span class="detail-label">Location:</span>
            <span class="detail-value">${bookingData.location}</span>
          </div>
          `
              : ""
          }
          <div class="detail-row">
            <span class="detail-label">Check-in:</span>
            <span class="detail-value">${bookingData.checkIn}</span>
          </div>
          ${
            bookingData.checkOut
              ? `
          <div class="detail-row">
            <span class="detail-label">Check-out:</span>
            <span class="detail-value">${bookingData.checkOut}</span>
          </div>
          `
              : ""
          }
          <div class="detail-row">
            <span class="detail-label">Status:</span>
            <span class="detail-value">
              <span class="status ${
                bookingData.status?.toLowerCase() || "pending"
              }">${bookingData.status || "PENDING"}</span>
            </span>
          </div>
          ${
            bookingData.notes
              ? `
          <div class="detail-row">
            <span class="detail-label">Notes:</span>
            <span class="detail-value">${bookingData.notes}</span>
          </div>
          `
              : ""
          }
        </div>

        <div class="contact-info">
          <h4>📞 Contact Information</h4>
          <p><strong>Phone:</strong> +251 11 123 4567</p>
          <p><strong>Email:</strong> <EMAIL></p>
          <p><strong>Website:</strong> www.kuriftu.com</p>
        </div>

        <p>If you need to modify or cancel your booking, please contact us at least 24 hours in advance.</p>

        <div style="text-align: center;">
          <a href="${
            process.env.NEXTAUTH_URL
          }/bookings" class="button">View My Bookings</a>
        </div>
      </div>

      <div class="footer">
        <p>Thank you for choosing Kuriftu Resorts!</p>
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>&copy; ${new Date().getFullYear()} Kuriftu Resorts. All rights reserved.</p>
      </div>
    </body>
    </html>
  `;

  try {
    await transporter.sendMail({
      from: `"Kuriftu Resorts" <${process.env.EMAIL_USER}>`,
      to: userEmail,
      subject: "Booking Confirmation - Kuriftu Resorts",
      html: emailTemplate,
    });

    console.log("Booking confirmation email sent successfully to:", userEmail);

    await logAuditEvent({
      action: "EMAIL_SENT",
      resource: "booking_confirmation",
      ip: "system",
      userAgent: "email_service",
      success: true,
    });
  } catch (error) {
    console.error("Error sending booking confirmation email:", error);

    await logAuditEvent({
      action: "EMAIL_FAILED",
      resource: "booking_confirmation",
      ip: "system",
      userAgent: "email_service",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    throw error;
  }
};

// Verify email configuration
export async function verifyEmailConfig(): Promise<boolean> {
  try {
    await transporter.verify();
    console.log("✅ Email configuration verified successfully");
    return true;
  } catch (error) {
    console.error("❌ Email configuration verification failed:", error);
    return false;
  }
}
