generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("MONGODB_URI")
}

model Resort {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  slug        String    @unique
  description String
  image       String
  location    String
  rooms       Room[]
  bookings    Booking[]
  createdAt   DateTime  @default(now())
}

model Room {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  resortId  String   @db.ObjectId
  name      String
  type      String
  price     Float
  image     String
  createdAt DateTime @default(now())

  resort Resort @relation(fields: [resortId], references: [id])
}

model SpaTreatment {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String
  price       Float
  duration    Int?      // Optional duration in minutes
  image       String
  category    String?   // Service category (Grooming, Wellness, Beauty, etc.)
  features    String[]  @default([]) // Array of service features
  isActive    Boolean   @default(true) // Whether the service is active/available
  bookings    Booking[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

model User {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  role          String    @default("user") // 'admin', 'manager', 'receptionist', 'user'
  accounts      Account[]
  sessions      Session[]
  createdAt     DateTime  @default(now())
}

model Account {
  id                String  @id @default(auto()) @map("_id") @db.ObjectId
  userId            String  @db.ObjectId
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  sessionToken String   @unique
  userId       String   @db.ObjectId
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Booking {
  id           String        @id @default(auto()) @map("_id") @db.ObjectId
  userEmail    String        // Consider adding User relation in future
  resortId     String?       @db.ObjectId
  spaId        String?       @db.ObjectId
  checkIn      DateTime
  checkOut     DateTime?
  notes        String?       @default("")
  status       BookingStatus @default(PENDING)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt

  // Relations
  resort       Resort?       @relation(fields: [resortId], references: [id], onDelete: Cascade)
  spaTreatment SpaTreatment? @relation(fields: [spaId], references: [id], onDelete: Cascade)

  // Indexes for performance
  @@index([userEmail])
  @@index([checkIn])
  @@index([status])
  @@index([resortId, checkIn])
  @@index([spaId, checkIn])
}

enum BookingStatus {
  PENDING
  CONFIRMED
  CANCELLED
}
