import { prisma } from "../lib/prisma";

async function main() {
  await prisma.resort.deleteMany();
  await prisma.spaTreatment.deleteMany();

  await prisma.resort.createMany({
    data: [
      {
        name: "Kurift<PERSON> Entoto",
        slug: "entoto",
        description: "Located on the breathtaking Entoto hills...",
        image: "/images/entoto.jpg",
      },
      {
        name: "Kuriftu Water Park",
        slug: "water-park",
        description: "Splash and relax at Africa’s largest water park...",
        image: "/images/water-park.jpg",
      },
      {
        name: "Kuriftu Awash Park",
        slug: "awash",
        description: "Experience wildlife, adventure and culture...",
        image: "/images/awash.jpg",
      },
      {
        name: "Kuriftu African Village",
        slug: "african-village",
        description: "A cultural getaway inspired by African heritage...",
        image: "/images/african-village.jpg",
      },
    ],
  });

  await prisma.spaTreatment.createMany({
    data: [
      {
        name: "Swedish Massage",
        description: "Relaxing full-body massage using light pressure.",
        price: 1200,
        image: "/images/massage.jpg",
      },
      {
        name: "Aromatherapy",
        description: "Massage with essential oils for healing & stress relief.",
        price: 1500,
        image: "/images/aroma.jpg",
      },
      {
        name: "Facial Treatment",
        description: "Cleanse, exfoliate, and hydrate your skin.",
        price: 1000,
        image: "/images/facial.jpg",
      },
    ],
  });

  await prisma.user.update({
    where: { email: "<EMAIL>" },
    data: { role: "admin" },
  });

  console.log("🌱 Seeded successfully!");
}

main().catch((e) => {
  console.error(e);
  process.exit(1);
});
