import { prisma } from "../lib/prisma";

async function main() {
  await prisma.resort.deleteMany();
  await prisma.spaTreatment.deleteMany();

  await prisma.resort.createMany({
    data: [
      {
        name: "Kurift<PERSON> Entoto",
        slug: "entoto",
        description: "Located on the breathtaking Entoto hills...",
        image: "/images/entoto.jpg",
      },
      {
        name: "Kuriftu Water Park",
        slug: "water-park",
        description: "Splash and relax at Africa’s largest water park...",
        image: "/images/water-park.jpg",
      },
      {
        name: "Kuriftu Awash Park",
        slug: "awash",
        description: "Experience wildlife, adventure and culture...",
        image: "/images/awash.jpg",
      },
      {
        name: "Kuriftu African Village",
        slug: "african-village",
        description: "A cultural getaway inspired by African heritage...",
        image: "/images/african-village.jpg",
      },
    ],
  });

  await prisma.spaTreatment.createMany({
    data: [
      {
        name: "Swedish Massage",
        description:
          "Relaxing full-body massage using light pressure to improve circulation and reduce stress.",
        price: 1200,
        duration: 60,
        image:
          "https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400&h=300&fit=crop",
        category: "Massage",
        features: ["Full Body", "Relaxation", "Stress Relief"],
        isActive: true,
      },
      {
        name: "Aromatherapy Massage",
        description:
          "Therapeutic massage with essential oils for healing and stress relief.",
        price: 1500,
        duration: 75,
        image:
          "https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=400&h=300&fit=crop",
        category: "Therapy",
        features: ["Essential Oils", "Aromatherapy", "Deep Relaxation"],
        isActive: true,
      },
      {
        name: "Rejuvenating Facial",
        description:
          "Deep cleansing and moisturizing facial treatment to refresh and revitalize your skin.",
        price: 1000,
        duration: 60,
        image:
          "https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400&h=300&fit=crop",
        category: "Skincare",
        features: ["Deep Cleansing", "Moisturizing", "Anti-Aging"],
        isActive: true,
      },
      {
        name: "Professional Barber Service",
        description:
          "Expert haircuts, beard trimming, and traditional shaving services with premium grooming products.",
        price: 150,
        duration: 45,
        image:
          "https://images.unsplash.com/photo-1503951914875-452162b0f3f1?w=400&h=300&fit=crop",
        category: "Grooming",
        features: ["Haircut", "Beard Trim", "Hot Towel Shave"],
        isActive: true,
      },
      {
        name: "Steam Sauna Experience",
        description:
          "Relax and detoxify in our traditional steam sauna with eucalyptus aromatherapy.",
        price: 200,
        duration: 60,
        image:
          "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop",
        category: "Wellness",
        features: ["Steam Therapy", "Aromatherapy", "Detoxification"],
        isActive: true,
      },
      {
        name: "Luxury Pedicure",
        description:
          "Complete foot care treatment including nail shaping, cuticle care, and relaxing foot massage.",
        price: 180,
        duration: 60,
        image:
          "https://images.unsplash.com/photo-1519415943484-9fa1873496d4?w=400&h=300&fit=crop",
        category: "Beauty",
        features: ["Nail Care", "Foot Massage", "Moisturizing"],
        isActive: true,
      },
      {
        name: "Professional Manicure",
        description:
          "Complete hand and nail care with nail shaping, cuticle treatment, and hand massage.",
        price: 120,
        duration: 45,
        image:
          "https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400&h=300&fit=crop",
        category: "Beauty",
        features: ["Nail Shaping", "Cuticle Care", "Hand Massage"],
        isActive: true,
      },
      {
        name: "Body Scrub & Wrap",
        description:
          "Exfoliating body scrub followed by nourishing body wrap for silky smooth skin.",
        price: 280,
        duration: 80,
        image:
          "https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=400&h=300&fit=crop",
        category: "Skincare",
        features: ["Exfoliation", "Body Wrap", "Moisturizing"],
        isActive: true,
      },
    ],
  });

  await prisma.user.update({
    where: { email: "<EMAIL>" },
    data: { role: "admin" },
  });

  console.log("🌱 Seeded successfully!");
}

main().catch((e) => {
  console.error(e);
  process.exit(1);
});
